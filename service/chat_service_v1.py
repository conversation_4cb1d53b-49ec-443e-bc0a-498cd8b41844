import traceback
import <PERSON><PERSON><PERSON><PERSON>

from config.chat_config import REFUS<PERSON>_MESSAGE_DICT, CAND<PERSON>ATE_ITEM_SIZE
from config.prompts import INTENT_CATEGORY_DICT, REJECT_QA_CAT, FREE_QA_CAT, COMPARE_INTENT
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import Chat<PERSON><PERSON>ponse
from core.enum.event_type import EventType
from core.schema.chat_response_data import ChatR<PERSON>ponseData
from core.enum.message_type import MessageType
from core.schema.constant import CHAT, EXTRACT_INTENT, MATCH_ITEM_EXACT, EXTRACT_TAG, REWRITE_QUERY, RETRIEVE_KNOWLEDGE
from service.chat_service_base import ChatServiceBase
from service.task_manager import TaskManager
from util.common_util import is_empty, not_empty, get_chat_request_key
from core.processor import normalize_item_name
from util.string_util import has_valid_info


class ChatServiceV1(ChatServiceBase):

    async def chat_inner(self, chat_request: ChatRequest):
        self._counter.labels(object=CHAT, condition="530").inc()
        if self.should_filtered_by_start_rule(chat_request):
            for response in self.refuse_answer(MessageType.FILTERED_BY_START_RULE, chat_request):
                yield response
            return

        chat_request_key = get_chat_request_key(chat_request)
        chat_request.chat_request_key = chat_request_key
        if chat_request.use_cache_answer and self._redis_manager.exists(chat_request_key):
            response = self._redis_manager.get(chat_request_key)
            # 如果命中redis缓存，则即刻返回
            for response in self.cache_answer(response, MessageType.CACHE_ANSWER):
                yield response
            return
        self.submit_tasks(chat_request)

        # 意图判断
        answer_intent = await chat_request.task_dict[EXTRACT_INTENT]
        chat_request.answer_intent = answer_intent
        # ToDo(hm): 暂时去掉语言的识别，等后面有需求了再说
        chat_request.recognize_language = chat_request.language
        if chat_request.ending_message.type == MessageType.ITEM_CONFIRM:
            # 用户确认机型
            confirmed_item = chat_request.ending_message.item_list[0]
            old_selected_item = self.get_item_by_name(chat_request.item_name)
            chat_request.item_id = confirmed_item.item_id
            chat_request.item_name = confirmed_item.item_name
            chat_request.category_id = confirmed_item.category_id
            chat_request.category_name = confirmed_item.category_name
            chat_request.item_name_normalized = normalize_item_name(confirmed_item.item_name)
            async for response in self.single_round_chat(chat_request, old_selected_item):
                yield response
            return

        chat_request.logger.debug(f"意图识别: {answer_intent}")
        if answer_intent in INTENT_CATEGORY_DICT[REJECT_QA_CAT]:
            # 自由拒答
            system_prompt, user_prompt = self._prompt_build_service.build_free_question_reject_prompt(chat_request)
            async for chat_response in self.generate_response_stream(system_prompt, user_prompt, chat_request,
                                                                     MessageType.FREE_FAQ_REJECT):
                yield chat_response
            return

        if answer_intent in INTENT_CATEGORY_DICT[FREE_QA_CAT]:
            # 自由问答
            system_prompt, user_prompt = self._prompt_build_service.build_free_question_prompt(chat_request)
            async for chat_response in self.generate_response_stream(system_prompt, user_prompt, chat_request,
                                                                     MessageType.FREE_FAQ_ANSWER):
                yield chat_response
            return

        if answer_intent == COMPARE_INTENT:
            # 双机对比
            async for response in self.item_compare_response_stream(chat_request):
                yield response
            return

        # 问答类
        # 当前对话类型，目前有2种类型：1-文本 7-用户确认的机型 （这里有如思操作，感觉可以借鉴）
        async for response in self.question_answer_response_stream(chat_request):
            yield response
        return

    def submit_tasks(self, chat_request: ChatRequest):
        # 创建并启动所有任务，先不着急 await 而是等到真正用到的时候再拿（延迟加载）
        chat_request.task_dict = dict()
        chat_request.task_dict[EXTRACT_INTENT] = TaskManager.create_task(
            self._query_parse_service.tag_question_first(chat_request), key=0)
        chat_request.task_dict[EXTRACT_TAG] = TaskManager.create_task(
            self._query_parse_service.recognize_tags(chat_request), key=1)
        chat_request.task_dict[REWRITE_QUERY] = TaskManager.create_task(
            self._query_parse_service.rewrite_query(chat_request), key=2)
        chat_request.task_dict[MATCH_ITEM_EXACT] = TaskManager.create_task(
            self._query_parse_service.recognize_item_names_exact(chat_request), key=3)

    # 单机问答
    async def single_round_chat(self, chat_request: ChatRequest, old_selected_item=None):
        # 如果数据中暂时不支持当前机型，直接拒答
        if chat_request.item_name_normalized not in self._normalized_item_name_list:
            chat_request.logger.error(f"接收到不支持的机型：{chat_request.item_name_normalized}")
            chat_request.logger.debug(f"当前支持的机型：{self._normalized_item_name_list}")
            # 返回拒答固定话术
            for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM, chat_request):
                yield response
            return

        self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
        response_selected_item = self.get_item_by_name(chat_request.item_name)
        second_tags = await chat_request.task_dict[EXTRACT_TAG]
        chat_request.second_tags = second_tags
        second_tags_map_dict = self._query_parse_service.get_second_tag_map_dict_from_tag_list_and_language(
            second_tags, chat_request.recognize_language.get_chinese_name())
        async for response in self.generate_answer_response_stream(chat_request, second_tags_map_dict):
            if response_selected_item and response_selected_item.is_xiaomi:
                # 更新吸顶机型（目前只有小米的机型可以被吸顶）
                response.data.selected_item = response_selected_item
            else:
                response.data.selected_item = old_selected_item
            yield response
        return

    # 用户确认机型回答-530
    @staticmethod
    def user_selected_answer(content, updated_item_list, answer_type):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type, item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type, item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.FINISH_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type, item_list=updated_item_list)
        )
        yield chat_response

    async def generate_answer_response_stream(self, chat_request, second_tags_map_dict):
        if is_empty(chat_request.item_name):
            chat_request.logger.error("单机参数问答时没有传入 item_name")
        pre_thinking_str = self.get_pre_thinking_str(chat_request, second_tags_map_dict)
        # markdown 斜体
        # wrapped_pre_thinking_str = f"*{pre_thinking_str}*\n"
        # 前端自定义的引用格式
        wrapped_pre_thinking_str = f"%% {pre_thinking_str}\n"
        for chat_response in self.wrap_str_to_response_stream(wrapped_pre_thinking_str, answer_type=MessageType.TEXT,
                                                              need_finish_event=False):
            yield chat_response

        chat_request.rewritten_query = await chat_request.task_dict[REWRITE_QUERY]
        chat_request.task_dict[RETRIEVE_KNOWLEDGE] = TaskManager.create_task(
            self._prompt_build_service.retrieve_doc_knowledge(chat_request, chat_request.rewritten_query,
                                                              second_tags_map_dict, chat_request.item_name_normalized),
            name="知识检索任务_DOC",
        )
        knowledge_dict = dict()
        try:
            knowledge_dict["知识检索任务_DOC"] = await chat_request.task_dict[RETRIEVE_KNOWLEDGE]
        except Exception as e:
            chat_request.logger.error(
                f"知识检索任务_DOC失败，prompt 构建时信息可能缺失 {str(e)}: {traceback.format_exc()}")
        self.log_elapse("检索完知识，构建 prompt", chat_request)
        system_prompt, user_prompt = self._prompt_build_service.build_prompt(chat_request, knowledge_dict)
        if is_empty(knowledge_dict):
            for response in self.refuse_answer(MessageType.NO_KNOWLEDGE, chat_request):
                yield response
            return

        async for response in self.generate_response_stream(system_prompt, user_prompt, chat_request, MessageType.TEXT,
                                                            need_first_event=False,
                                                            pre_thinking_str=wrapped_pre_thinking_str):
            yield response

    def provide_candidate_items(self, candidate_item_names, query):
        if is_empty(candidate_item_names):
            return list()

        candidates = candidate_item_names[:CANDIDATE_ITEM_SIZE]
        candidates = list(set(candidates))
        candidates = sorted(candidates,
                            key=lambda item: 1.0 - Levenshtein.distance(query, item) / max(len(query), len(item)),
                            reverse=True)
        candidates = [self.get_item_by_name(name) for name in candidates]
        return candidates

    # 问答类意图响应-530
    async def question_answer_response_stream(self, chat_request: ChatRequest):
        language = chat_request.language
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language

        matched_item_name_dict = await chat_request.task_dict[MATCH_ITEM_EXACT]
        if is_empty(matched_item_name_dict):
            # 用户问题中没有涉及具体机型
            if is_empty(chat_request.item_name):
                # 没有吸顶
                chat_request.logger.debug("未识别出任何机型，也没有吸顶，拒答")
                for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM, chat_request):
                    yield response
                return

            # 有吸顶
            chat_request.logger.debug("未识别出任何机型，使用原始的吸顶机型作答")
            async for response in self.single_round_chat(chat_request):
                yield response
            return

        if len(matched_item_name_dict) == 1:
            # 用户问题只涉及一个机型
            first_key = list(matched_item_name_dict.keys())[0]
            if len(matched_item_name_dict[first_key]) == 0:
                # 没有匹配上任何型号，拒答
                for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM, chat_request):
                    yield response
                return

            if len(matched_item_name_dict[first_key]) == 1:
                chat_request.logger.debug("只识别出一个机型，直接进入参数问答流程")
                old_selected_item = self.get_item_by_name(chat_request.item_name)
                chat_request.item_name = matched_item_name_dict[first_key][0]
                chat_request.item_name_normalized = normalize_item_name(chat_request.item_name)
                async for response in self.single_round_chat(chat_request, old_selected_item):
                    yield response
                return

            chat_request.logger.debug("识别出多个机型，让用户选择")
            chat_request.rewritten_query = await chat_request.task_dict[REWRITE_QUERY]
            candidates = self.provide_candidate_items(matched_item_name_dict[first_key], chat_request.rewritten_query)
            content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_CANDIDATE][language]
            for response in self.user_selected_answer(content, candidates,
                                                      answer_type=MessageType.ITEM_CANDIDATE):
                yield response
            return

        # 用户问题涉及多个机型：如果意图识别正确应该不会走到这里，先暂时拒答
        for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM, chat_request):
            yield response
        return

    # 双机对比意图响应
    async def item_compare_response_stream(self, chat_request: ChatRequest):
        matched_item_name_dict = await chat_request.task_dict[MATCH_ITEM_EXACT]
        first_item, second_item = self.get_compare_item_pair(chat_request.item_name, matched_item_name_dict)
        if first_item is None or second_item is None or first_item.item_name == second_item.item_name:
            # 没有成功提取要对比的两个机型，则拒答，不更新吸顶机型
            for response in self.refuse_answer(MessageType.ITEM_COMPARE, chat_request):
                yield response
            return

        chat_request.logger.debug(f"进行双机对比，机型列表: {first_item} vs {second_item}")
        pre_thinking_str = self.get_pre_thinking_for_item_compare(chat_request, first_item, second_item)
        wrapped_pre_thinking_str = f"%% {pre_thinking_str}\n"
        for chat_response in self.wrap_str_to_response_stream(wrapped_pre_thinking_str, answer_type=MessageType.TEXT,
                                                              need_finish_event=False):
            yield chat_response
        system_prompt, user_prompt = self._prompt_build_service.build_item_compare_prompt(
            chat_request, [first_item.item_name, second_item.item_name])
        if system_prompt and user_prompt:
            async for chat_response in self.generate_response_stream(system_prompt, user_prompt, chat_request,
                                                                     MessageType.ITEM_COMPARE):
                chat_response.data.selected_item = self.get_current_selected_item(first_item, second_item, chat_request)
                chat_response.data.item_list = self.get_current_item_list(first_item, second_item,
                                                                          chat_response.data.selected_item)
                yield chat_response
        else:
            for response in self.refuse_answer(MessageType.ITEM_COMPARE_FAILED, chat_request):
                yield response
            return

    def get_current_selected_item(self, first_item, second_item, chat_request):
        if chat_request.item_name in (first_item.item_name, second_item.item_name):
            # 如果当前机型在对比的两个机型中，保持不变
            return self.get_item_by_name(chat_request.item_name)

        # 只有小米机型可以吸顶
        if first_item.is_xiaomi:
            return first_item

        if second_item.is_xiaomi:
            return second_item

        return None

    @staticmethod
    def get_current_item_list(first_item, second_item, selected_item):
        if selected_item is not None:
            # 如果有吸顶机型，保证第一个是吸顶机型（用于看板展示）
            if selected_item.item_name == first_item.item_name:
                return [first_item, second_item]
            if selected_item.item_name == second_item.item_name:
                return [second_item, first_item]

        # 只有包含小米机型才跳转，且第一个必须是小米机型
        if first_item.is_xiaomi:
            return [first_item, second_item]

        if second_item.is_xiaomi:
            return [second_item, first_item]

        return None

    def get_compare_item_pair(self, old_xiaomi_selected_item_name, matched_item_name_dict):
        matched_item_dict = dict()
        for raw_item_name in matched_item_name_dict:
            if raw_item_name not in matched_item_dict:
                matched_item_dict[raw_item_name] = list()

            for matched_item_name in matched_item_name_dict[raw_item_name]:
                item = self.get_item_by_name(matched_item_name)
                if item is None:
                    continue

                matched_item_dict[raw_item_name].append(item)
            if len(matched_item_dict[raw_item_name]) != 1:
                # 目前双机对比不支持让用户确认模糊机型，所以匹配上多个机型就直接拒答
                return None, None

        item_num = len(matched_item_dict)
        if item_num == 0:
            return None, None

        if item_num == 1:
            if is_empty(old_xiaomi_selected_item_name):
                # 没有吸顶，只有一个机型，无法进行对比
                return None, None

            # 有吸顶
            first_key = list(matched_item_dict.keys())[0]
            return self.get_item_by_name(old_xiaomi_selected_item_name), matched_item_dict[first_key][0]

        if item_num == 2:
            # 用户输入了两个机型，此时忽略吸顶机型
            raw_item_name_list = list(matched_item_dict.keys())
            return matched_item_dict[raw_item_name_list[0]][0], matched_item_dict[raw_item_name_list[1]][0]

        # 不支持多于 2 个机型对比
        return None, None

    def get_compare_item_pair_old(self, old_xiaomi_selected_item_name, exact_item_names):
        xiaomi_item_list = list()
        non_xiaomi_item_list = list()
        for item_name in exact_item_names:
            item = self.get_item_by_name(item_name)
            if item is None:
                continue

            if item.is_xiaomi:
                xiaomi_item_list.append(item)
            else:
                non_xiaomi_item_list.append(item)

        item_num = len(xiaomi_item_list) + len(non_xiaomi_item_list)
        if item_num == 0:
            return None, None

        if item_num == 1:
            if is_empty(old_xiaomi_selected_item_name):
                # 只有一个机型，无法进行对比
                return None, None

            second_item_name = xiaomi_item_list[0] if not_empty(xiaomi_item_list) else non_xiaomi_item_list[0]
            return self.get_item_by_name(old_xiaomi_selected_item_name), second_item_name

        if item_num == 2:
            # 用户输入了两个机型，此时忽略吸顶机型
            if len(non_xiaomi_item_list) == 0:
                return xiaomi_item_list[0], xiaomi_item_list[1]

            if len(non_xiaomi_item_list) == 1:
                return xiaomi_item_list[0], non_xiaomi_item_list[0]

            # 支持两个非小米机型对比
            return non_xiaomi_item_list[0], non_xiaomi_item_list[1]

        # 不支持多于 2 个机型对比：有可能用户输入了多个机型，也有可能用户输入 2 个机型，但匹配上了多个模糊机型
        return None, None

    @staticmethod
    def should_filtered_by_start_rule(chat_request: ChatRequest):
        if len(chat_request.chat_history) > 1:
            return False

        first_message = "".join([message.content for message in chat_request.chat_history[0].messages])
        # 如果首句只输入了标点符号，直接回应
        return not has_valid_info(first_message)
