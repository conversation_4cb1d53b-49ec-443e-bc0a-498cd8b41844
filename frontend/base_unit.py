import asyncio

import aiohttp

from config.run_config import RUN_CONFIG_DICT, DOMAIN, API_ACCESS_TOKEN
from core.schema.chat_request import ChatRequest, Message
from core.schema.chat_response import ChatResponse
from core.enum.event_type import EventType
from core.schema.chat_response_data import Chat<PERSON><PERSON>ponseData
from core.schema.constant import QUOTE_SIGN
from core.enum.doc_type import DocType
from core.enum.content_type import ContentType
from util.common_util import is_empty, decode_sse, pprint, not_empty

HOST_PROJECT_DIR = "/home/<USER>/workspace/inference"


# ToDo(hm): 流式转批式处理，但没法看出来动态轮播的效果了，感受可能和线上有区别
async def async_process_chat_request_530(chat_request: ChatRequest, env):
    """Async function to process a chat request and return the response text, whether it has item_list, and the item_list"""
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/chat"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": chat_request.request_id,
    }

    # Convert the ChatRequest to a dictionary for the API request
    data = chat_request.to_dict()
    # 只取最后的 event=3 时的数据
    chat_request.debug = True

    max_retries = 3
    last_error = None

    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        error_msg = f"请求失败，状态码: {response.status},响应内容:{error_text}"
                        print(f"第 {attempt + 1} 次请求失败: {error_msg}")

                        if attempt == max_retries - 1:  # 最后一次尝试
                            return ChatResponse(
                                event=EventType.FINISH_EVENT,
                                data=ChatResponseData(text=error_msg)
                            )
                        continue  # 重试

                    # 处理成功响应
                    async for line in response.content:
                        # 去除多余的新行和空行
                        chunk = line.decode("utf-8").strip()
                        if is_empty(chunk):
                            continue

                        response_dict = decode_sse(chunk)
                        if is_empty(response_dict) or "event" not in response_dict:
                            continue

                        chat_response = ChatResponse.from_dict(response_dict)
                        if chat_response.event == EventType.FINISH_EVENT:
                            print("here" * 10)
                            pprint(chat_response)
                            return chat_response

        except Exception as e:
            last_error = e
            print(f"第 {attempt + 1} 次请求发生异常: {str(e)}")

            if attempt == max_retries - 1:  # 最后一次尝试
                return ChatResponse(
                    event=EventType.FINISH_EVENT,
                    data=ChatResponseData(
                        text=f"请求失败，重试 {max_retries} 次后仍然失败。最后错误: {str(last_error)}"
                    )
                )

            # 等待一段时间后重试
            await asyncio.sleep(1)

    # 如果所有重试都失败了，返回错误响应
    return ChatResponse(
        event=EventType.FINISH_EVENT,
        data=ChatResponseData(
            text=f"请求失败，重试 {max_retries} 次后仍然失败。最后错误: {str(last_error) if last_error else '未知错误'}"
        )
    )


def format_doc_trace_sources(doc_trace_source_list):
    """格式化文档来源信息"""
    if not doc_trace_source_list:
        return ""

    # 按照doc_type分组
    doc_type_groups = {}
    for source in doc_trace_source_list:
        doc_type = source.doc_type
        if doc_type not in doc_type_groups:
            doc_type_groups[doc_type] = []
        doc_type_groups[doc_type].append(source)

    source_sections = []

    for doc_type, sources in doc_type_groups.items():
        # 根据doc_type确定标题
        if doc_type == DocType.FAQ:
            section_title = "📋 常见问答"
        elif doc_type == DocType.SALE_TOOLS:
            section_title = "📚 培训文档"
        elif doc_type == DocType.PRODUCT_PARAM:
            section_title = "⚙️ 产品参数"
        elif doc_type == DocType.PRODUCT_INTRODUCTION:
            section_title = "📱 产品详情"
        else:
            section_title = f"📄 文档类型{doc_type.value}"

        source_items = []
        for i, source in enumerate(sources, 1):
            # 根据content_type决定如何展示内容
            if source.content_type == ContentType.HTML:
                # HTML内容，显示标题和部分内容
                content_preview = source.content[:100] + "..." if source.content and len(source.content) > 100 else source.content
                source_items.append(f"   {i}. **{source.title}**: {content_preview}")
            elif source.content_type == ContentType.URL:
                # URL链接
                source_items.append(f"   {i}. **{source.title}**: [查看详情]({source.content})")
            elif source.content_type == ContentType.KEY:
                # 关键信息
                source_items.append(f"   {i}. **{source.title}**: {source.content}")
            else:
                # 默认处理
                source_items.append(f"   {i}. **{source.title}**: {source.content}")

        if source_items:
            section_content = "\n".join(source_items)
            source_sections.append(f"{section_title}:\n{section_content}")

    if source_sections:
        return "\n\n---\n**📖 参考来源**:\n\n" + "\n\n".join(source_sections)

    return ""


def enhance_msg(response: ChatResponse):
    response_data = response.data
    first_token_elapse = (response_data.answer_start_time - response_data.request_receive_time) / 1000
    answer_elapse = (response_data.answer_finish_time - response_data.answer_start_time) / 1000
    answer_type = response_data.answer_type
    answer_type_chinese = answer_type.description
    suffix_info_list = [
        f"开始回答耗时={first_token_elapse:.2f}秒",
        f"回答耗时={answer_elapse}秒",
        f"model_version={response_data.model_version}",
        f"response_id={response.request_id}",
        f"回答消耗总tokens={response_data.total_tokens}",
        f"回答类型={answer_type.value}:{answer_type_chinese}",
        f"selected_item={response_data.selected_item}",
        f"item_list={response_data.item_list}",
        f"time_cost={response_data.time_cost}"
    ]
    suffix_info = ",".join(suffix_info_list)
    # 实际前端只需要一个 \n 就能换行（非标准 mk），st 用的是标准 mk，需要两个 \n 才能换行
    added_text = response_data.text.replace("\n", "\n\n")
    # 前端用 %% 表示引用，标准的 mk 用 >
    added_text = added_text.replace(QUOTE_SIGN, ">")

    # 添加文档来源信息
    doc_sources_text = ""
    if not_empty(response_data.doc_trace_source_list):
        doc_sources_text = format_doc_trace_sources(response_data.doc_trace_source_list)

    return f"{added_text}{doc_sources_text}\n\n({suffix_info})"


def response_data_to_message(response_data: ChatResponseData) -> Message:
    """Convert ChatResponseData to a Message object

    This function maps the fields from ChatResponseData to Message:
    - type = answer_type
    - content = text
    - selected_item and item_list are directly mapped

    Returns:
        Message: A Message object with data from the ChatResponseData
    """
    # Convert Item objects to dictionaries to avoid validation errors
    selected_item_dict = None
    if response_data.selected_item:
        selected_item_dict = response_data.selected_item.model_dump()

    item_list_dict = None
    if response_data.item_list:
        item_list_dict = [item.model_dump() for item in response_data.item_list]

    return Message(
        type=response_data.answer_type,
        content=response_data.text,
        selected_item=selected_item_dict,
        item_list=item_list_dict
    )


def call_chat(chat_request: ChatRequest, env="local"):
    """Process a chat request and return the response text, whether it has item_list, the item_list, and answer_type"""
    # Run the async function in a synchronous context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        print("chat request:")
        pprint(chat_request.to_dict())
        return loop.run_until_complete(async_process_chat_request_530(chat_request, env))
    finally:
        loop.close()
